{"$schema": "https://biomejs.dev/schemas/1.9.4/schema.json", "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true}, "files": {"ignoreUnknown": false, "ignore": ["packages/ui/src/**/*", "**/node_modules/**/*", "**/.react-router/**/*", "**/worker-configuration.d.ts", "**/dist/**/*", "**/build/**/*", "**/dist/*", "**/public/**/*"]}, "formatter": {"enabled": true, "indentWidth": 2, "indentStyle": "space", "lineEnding": "lf", "formatWithErrors": true, "lineWidth": 80}, "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": true, "style": {"useImportType": "warn", "noParameterAssign": "off", "noNonNullAssertion": "off"}, "correctness": {"noUnusedVariables": "warn", "noUnusedImports": "warn", "noUnreachable": "warn", "noEmptyPattern": "off", "useExhaustiveDependencies": "off"}, "a11y": {"noSvgWithoutTitle": "off", "useSemanticElements": "off"}, "complexity": {"noForEach": "off", "noThisInStatic": "off", "noStaticOnlyClass": "off"}}}, "javascript": {"formatter": {"quoteStyle": "double", "semicolons": "always"}}}