{"version": 3, "file": "s3.js", "sourceRoot": "", "sources": ["../../src/providers/s3.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,WAAW,CAAC;AAEtC,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AAO5C,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,MAAM,UAAU,CAAC;AAElD,MAAM,OAAO,iBAAiB;IACpB,GAAG,CAAY;IACf,MAAM,CAAS;IACf,MAAM,CAAgB;IAE9B,YAAY,MAAqB;QAC/B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,GAAG,GAAG,IAAI,SAAS,CAAC;YACvB,WAAW,EAAE,MAAM,CAAC,WAAW;YAC/B,eAAe,EAAE,MAAM,CAAC,eAAe;YACvC,OAAO,EAAE,IAAI;YACb,MAAM,EAAE,MAAM,CAAC,MAAM,IAAI,MAAM;SAChC,CAAC,CAAC;QAEH,IAAI,MAAM,CAAC,QAAQ,KAAK,IAAI,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;YACjD,IAAI,CAAC,MAAM,GAAG,WAAW,MAAM,CAAC,SAAS,6BAA6B,MAAM,CAAC,MAAM,EAAE,CAAC;QACxF,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,MAAM;gBACT,MAAM,CAAC,QAAQ;oBACf,cAAc,MAAM,CAAC,MAAM,kBAAkB,MAAM,CAAC,MAAM,EAAE,CAAC;QACjE,CAAC;IACH,CAAC;IACD,KAAK,CAAC,uBAAuB,CAC3B,WAAmB,EACnB,aAAsB;QAEtB,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,WAAW,CAAC,CAAC;QACjC,GAAG,CAAC,YAAY,CAAC,GAAG,CAClB,8BAA8B,EAC9B,yBAAyB,kBAAkB,CAAC,aAAa,IAAI,SAAS,CAAC,GAAG,CAC3E,CAAC;QACF,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,uBAAuB,EAAE,0BAA0B,CAAC,CAAC;QAC1E,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;QAC9C,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAChC,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE;YAC1B,MAAM,EAAE,KAAK;SACd,CAAC,EACF;YACE,GAAG,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE;SACzB,CACF,CAAC;QACF,OAAO,MAAM,CAAC,GAAG,CAAC;IACpB,CAAC;IAED,KAAK,CAAC,qBAAqB,CAAC,GAAW;QACrC,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,IAAI,GAAG,EAAE,CAAC;QACpC,MAAM,KAAK,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;QAC3B,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,0BAA0B,EAAE,QAAQ,CAAC,CAAC;QAC7D,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAC7B,IAAI,OAAO,CAAC,KAAK,EAAE;YACjB,MAAM,EAAE,KAAK;SACd,CAAC,EACF;YACE,GAAG,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE;SACzB,CACF,CAAC;QACF,OAAO,GAAG,CAAC,GAAG,CAAC;IACjB,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,GAAW,EAAE,IAAwB;QAChD,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,IAAI,GAAG,EAAE,CAAC;QACpC,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE;YACpC,MAAM,EAAE,KAAK;YACb,OAAO,EAAE;gBACP,gBAAgB,EACd,IAAI,YAAY,IAAI;oBAClB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;oBACtB,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE;aACjC;YACD,IAAI,EAAE,IAAI;SACX,CAAC,CAAC;QAEH,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC;YACZ,MAAM,IAAI,KAAK,CAAC,kBAAkB,GAAG,CAAC,MAAM,IAAI,MAAM,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;QACtE,CAAC;QAED,MAAM,IAAI,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACrC,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;QAC3C,CAAC;QACD,MAAM,IAAI,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;QAE7B,OAAO;YACL,IAAI;YACJ,GAAG,EAAE,GAAG,CAAC,GAAG;YACZ,QAAQ,EAAE,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC;YAC/B,IAAI;SACL,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,IAAY;QACjB,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC;YAC3B,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YACxD,MAAM,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAC7C,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ;gBACzB,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,GAAG,QAAQ,EAAE;gBACtC,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,QAAQ,EAAE,CAAC;QAClC,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ;gBACzB,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,IAAI,EAAE;gBACnC,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,IAAI,IAAI,EAAE,CAAC;QAC/B,CAAC;IACH,CAAC;IAED,MAAM,CAAC,GAAW;QAChB,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;YAC1B,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;YAC9D,OAAO,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACrC,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,GAAG,CAAC;QACb,CAAC;IACH,CAAC;IAED,WAAW,CAAC,GAAW;QACrB,OAAO,GAAG,IAAI,CAAC,MAAM,IAAI,GAAG,EAAE,CAAC;IACjC,CAAC;IAED,KAAK,CAAC,qBAAqB,CACzB,GAAW,EACX,WAAmB;QAEnB,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,IAAI,GAAG,UAAU,CAAC;QAC5C,MAAM,KAAK,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;QAC3B,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,0BAA0B,EAAE,QAAQ,CAAC,CAAC;QAC7D,MAAM,OAAO,GAAG,IAAI,OAAO,CAAC,KAAK,CAAC,QAAQ,EAAE,EAAE;YAC5C,MAAM,EAAE,MAAM;YACd,OAAO,EAAE;gBACP,cAAc,EAAE,WAAW,IAAI,0BAA0B;aAC1D;YACD,IAAI,EAAE,SAAS;SAChB,CAAC,CAAC;QACH,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACnD,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,aAAa,CAAC,CAAC;QAC5C,MAAM,YAAY,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;QAC3C,MAAM,aAAa,GAAG,YAAY,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAC;QACvE,MAAM,QAAQ,GAAG,aAAa,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QAEzD,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;QACvD,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,GAAW,EAAE,QAAgB;QAC9C,IAAI,CAAC;YACH,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,IAAI,GAAG,aAAa,QAAQ,EAAE,CAAC;YACzD,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CACvC,IAAI,OAAO,CAAC,GAAG,EAAE;gBACf,MAAM,EAAE,KAAK;aACd,CAAC,EACF;gBACE,GAAG,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE;aACzB,CACF,CAAC;YACF,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,aAAa,CAAC,CAAC;YAC5C,MAAM,YAAY,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;YAC3C,MAAM,MAAM,GAAG,IAAI,SAAS,CAAC;gBAC3B,gBAAgB,EAAE,KAAK;gBACvB,iBAAiB,EAAE,IAAI;gBACvB,cAAc,EAAE,IAAI;aACrB,CAAC,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;YACxC,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;QACpE,CAAC;QAAC,MAAM,CAAC;YACP,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;QAC7C,CAAC;IACH,CAAC;IAED,KAAK,CAAC,qBAAqB,CACzB,GAAW,EACX,QAAgB,EAChB,UAAkB;QAElB,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,IAAI,GAAG,eAAe,UAAU,aAAa,QAAQ,EAAE,CAAC;QAClF,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CACvC,IAAI,OAAO,CAAC,GAAG,EAAE;YACf,MAAM,EAAE,KAAK;SACd,CAAC,EACF;YACE,GAAG,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE;SACzB,CACF,CAAC;QACF,OAAO,aAAa,CAAC,GAAG,CAAC;IAC3B,CAAC;IAED,KAAK,CAAC,uBAAuB,CAC3B,GAAW,EACX,QAAgB,EAChB,KAA4B;QAE5B,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,IAAI,GAAG,aAAa,QAAQ,EAAE,CAAC;QACzD,MAAM,IAAI,GAAG,4BAA4B,KAAK;aAC3C,GAAG,CACF,CAAC,IAAI,EAAE,EAAE,CACP,qBAAqB,IAAI,CAAC,UAAU,sBAAsB,IAAI,CAAC,IAAI,gBAAgB,CACtF;aACA,IAAI,CAAC,EAAE,CAAC,4BAA4B,CAAC;QAExC,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CACvC,IAAI,OAAO,CAAC,GAAG,EAAE;YACf,MAAM,EAAE,MAAM;YACd,OAAO,EAAE;gBACP,cAAc,EAAE,iBAAiB;aAClC;YACD,IAAI;SACL,CAAC,CACH,CAAC;QAEF,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,aAAa,CAAC,CAAC;QAC5C,MAAM,YAAY,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;QAE3C,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CACb,qCAAqC,QAAQ,CAAC,MAAM,IAAI,YAAY,EAAE,CACvE,CAAC;QACJ,CAAC;QAED,MAAM,QAAQ,GAAG,WAAW,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;QACvD,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,KAAK,CACb,gEAAgE,CACjE,CAAC;QACJ,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,KAAK,CAAC,oBAAoB,CAAC,GAAW,EAAE,QAAgB;QACtD,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,IAAI,GAAG,aAAa,QAAQ,EAAE,CAAC;QACzD,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CACvC,IAAI,OAAO,CAAC,GAAG,EAAE;YACf,MAAM,EAAE,QAAQ;SACjB,CAAC,CACH,CAAC;QAEF,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,aAAa,CAAC,CAAC;QAC5C,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,kCAAkC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;QACvE,CAAC;IACH,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,GAAW;QAQ3B,MAAM,MAAM,GAAG,WAAW,IAAI,CAAC,MAAM,CAAC,SAAS,6BAA6B,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC;QAClG,MAAM,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QACjC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAChC,IAAI,OAAO,CAAC,GAAG,EAAE;YACf,MAAM,EAAE,KAAK;SACd,CAAC,EACF;YACE,GAAG,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE;SACzB,CACF,CAAC;QAEF,IAAI,MAAM,EAAE,CAAC;YACX,MAAM,GAAG,GAAG,MAAM,KAAK,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;YAC5C,MAAM,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC;YAC5B,MAAM,IAAI,GAAG,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YACjC,MAAM,IAAI,GAAG,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;YAC3C,MAAM,IAAI,GAAG,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;YACzC,MAAM,IAAI,GAAG,MAAM,GAAG,CAAC,WAAW,EAAE,CAAC;YACrC,OAAO;gBACL,IAAI;gBACJ,IAAI,EAAE,IAAI,IAAI,0BAA0B;gBACxC,GAAG;gBACH,IAAI;gBACJ,GAAG,EAAE,GAAG,CAAC,GAAG;gBACZ,IAAI;aACL,CAAC;QACJ,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;CACF"}